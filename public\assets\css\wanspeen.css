/* Wanspeen Tourism Platform - Custom Styles */

:root {
    --primary-green: #058507;
    --primary-orange: #ffa500;
    --primary-maroon: #961203;
    --light-green: #e6f7e6;
    --light-orange: #fff3e0;
    --light-maroon: #f8e6e4;
    --dark-text: #2c3e50;
    --light-text: #6c757d;
    --hero-gradient: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green) 85%, var(--primary-orange) 95%, var(--primary-maroon) 100%);
}

/* Global Styles */
body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
}

/* Custom Button Styles */
.btn-wanspeen-primary {
    background: var(--primary-green);
    border-color: var(--primary-green);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-wanspeen-primary:hover {
    background: #218838;
    border-color: #218838;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-wanspeen-secondary {
    background: var(--primary-orange);
    border-color: var(--primary-orange);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-wanspeen-secondary:hover {
    background: #e69500;
    border-color: #e69500;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
}

.btn-wanspeen-accent {
    background: var(--primary-maroon);
    border-color: var(--primary-maroon);
    color: white;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-wanspeen-accent:hover {
    background: #7a0f02;
    border-color: #7a0f02;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(150, 18, 3, 0.3);
}

/* Card Styles */
.card-wanspeen {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.card-wanspeen:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Background Utilities */
.bg-wanspeen-green {
    background-color: var(--primary-green) !important;
}

.bg-wanspeen-orange {
    background-color: var(--primary-orange) !important;
}

.bg-wanspeen-maroon {
    background-color: var(--primary-maroon) !important;
}

.bg-wanspeen-light-green {
    background-color: var(--light-green) !important;
}

.bg-wanspeen-light-orange {
    background-color: var(--light-orange) !important;
}

.bg-wanspeen-light-maroon {
    background-color: var(--light-maroon) !important;
}

/* Text Color Utilities */
.text-wanspeen-green {
    color: var(--primary-green) !important;
}

.text-wanspeen-orange {
    color: var(--primary-orange) !important;
}

.text-wanspeen-maroon {
    color: var(--primary-maroon) !important;
}

/* Logo Styles */
.wanspeen-logo {
    font-weight: 700;
    font-size: 1.5rem;
}

.wanspeen-logo .logo-w {
    color: var(--primary-orange);
    font-size: 1.8rem;
}

/* Navigation Enhancements */
.navbar-wanspeen {
    background: rgba(40, 167, 69, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Form Enhancements */
.form-control:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-select:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Alert Enhancements */
.alert-wanspeen-success {
    background-color: var(--light-green);
    border-color: var(--primary-green);
    color: #155724;
}

.alert-wanspeen-warning {
    background-color: var(--light-orange);
    border-color: var(--primary-orange);
    color: #856404;
}

.alert-wanspeen-danger {
    background-color: var(--light-maroon);
    border-color: var(--primary-maroon);
    color: #721c24;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .btn-wanspeen-primary,
    .btn-wanspeen-secondary,
    .btn-wanspeen-accent {
        padding: 0.4rem 1.2rem;
        font-size: 0.9rem;
    }
    
    .wanspeen-logo {
        font-size: 1.3rem;
    }
    
    .wanspeen-logo .logo-w {
        font-size: 1.5rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Spinner */
.wanspeen-spinner {
    border: 3px solid var(--light-green);
    border-top: 3px solid var(--primary-green);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
