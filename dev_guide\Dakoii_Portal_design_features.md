# Wanspeen Dakoii Portal - Feature Design & User Stories

## Portal Identity

**Dakoii Portal**: Super Administrative Command Center for Wanspeen Tourism System
- **Access URL**: `https://wanspeen.com/dakoii`
- **User Type**: System Super Administrators
- **Purpose**: System-wide management, organization oversight, and global configuration
- **Security Level**: Highest - Multi-layered authentication and authorization

---

## Color Scheme & Design Theme

### Dark Professional Theme
The Dakoii Portal uses a sophisticated dark theme to distinguish it from the organization portal and convey authority and control.

#### Primary Color Palette
```css
:root {
    /* Dark Background Colors */
    --dakoii-dark-bg: #1a1d29;           /* Main background */
    --dakoii-darker-bg: #13151f;         /* Deeper sections */
    --dakoii-card-bg: #212530;           /* Card backgrounds */
    --dakoii-hover-bg: #2a2d3a;          /* Hover states */
    
    /* Accent Colors */
    --dakoii-primary: #6366f1;           /* Indigo - primary actions */
    --dakoii-primary-hover: #4f46e5;     /* Darker indigo */
    --dakoii-secondary: #8b5cf6;         /* Purple - secondary actions */
    --dakoii-accent: #06b6d4;            /* Cyan - highlights */
    
    /* Status Colors */
    --dakoii-success: #10b981;           /* Green - success states */
    --dakoii-warning: #f59e0b;           /* Amber - warnings */
    --dakoii-danger: #ef4444;            /* Red - danger/errors */
    --dakoii-info: #3b82f6;              /* Blue - information */
    
    /* Text Colors */
    --dakoii-text-primary: #f1f5f9;      /* Main text */
    --dakoii-text-secondary: #94a3b8;    /* Secondary text */
    --dakoii-text-muted: #64748b;        /* Muted text */
    
    /* Border & Divider Colors */
    --dakoii-border: #334155;            /* Borders */
    --dakoii-border-light: #475569;      /* Light borders */
    
    /* Gradient Accents */
    --dakoii-gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --dakoii-gradient-2: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --dakoii-gradient-3: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
}
```

#### Design Principles
1. **Dark Authority**: Deep, rich dark tones convey power and control
2. **High Contrast**: Excellent readability with light text on dark backgrounds
3. **Vibrant Accents**: Indigo and purple for primary actions, cyan for highlights
4. **Professional Polish**: Subtle shadows, smooth transitions, refined typography
5. **Visual Hierarchy**: Clear distinction between primary, secondary, and tertiary elements

#### Component Styling Examples
```css
/* Navigation Bar */
.dakoii-navbar {
    background: linear-gradient(90deg, var(--dakoii-darker-bg) 0%, var(--dakoii-dark-bg) 100%);
    border-bottom: 2px solid var(--dakoii-primary);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

/* Cards */
.dakoii-card {
    background: var(--dakoii-card-bg);
    border: 1px solid var(--dakoii-border);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    transition: transform 0.2s, box-shadow 0.2s;
}

.dakoii-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.2);
}

/* Buttons */
.btn-dakoii-primary {
    background: var(--dakoii-gradient-2);
    color: var(--dakoii-text-primary);
    border: none;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Stat Cards */
.stat-card {
    background: var(--dakoii-card-bg);
    border-left: 4px solid var(--dakoii-primary);
    padding: 1.5rem;
}

/* Tables */
.dakoii-table {
    background: var(--dakoii-card-bg);
    color: var(--dakoii-text-primary);
}

.dakoii-table thead {
    background: var(--dakoii-darker-bg);
    border-bottom: 2px solid var(--dakoii-primary);
}

.dakoii-table tbody tr:hover {
    background: var(--dakoii-hover-bg);
}
```

---

## Database Schema for Dakoii Portal

### Core Tables

```sql
-- =============================================
-- DAKOII PORTAL AUTHENTICATION & ACCESS
-- =============================================

-- Dakoii Users (Super Administrators)
CREATE TABLE dakoii_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    profile_photo_path VARCHAR(500),
    
    -- Access Control
    role ENUM('super_admin', 'admin', 'viewer') DEFAULT 'admin',
    
    
    
    -- Status
    is_active TINYINT(1) DEFAULT 1,
    
    
    -- Audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_active (is_active),
    INDEX idx_role (role),
    FOREIGN KEY (created_by) REFERENCES dakoii_users(id),
    FOREIGN KEY (updated_by) REFERENCES dakoii_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


-- =============================================
-- ORGANIZATIONS & SUBSCRIPTIONS MANAGEMENT
-- =============================================

-- Organizations (Enhanced for Dakoii Management)
CREATE TABLE organizations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    description TEXT,
    
    -- Contact Information
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state_province VARCHAR(100),
    country VARCHAR(100) DEFAULT 'Papua New Guinea',
    postal_code VARCHAR(20),
    website VARCHAR(200),
    
    -- Branding
    logo_path VARCHAR(500),
    banner_path VARCHAR(500),
    brand_color VARCHAR(7), -- Hex color
    
    -- Subscription & Limits
    subscription_type ENUM('trial', 'basic', 'premium', 'enterprise', 'custom') DEFAULT 'trial',
    subscription_status ENUM('active', 'suspended', 'expired', 'cancelled') DEFAULT 'active',
    subscription_starts_at DATE,
    subscription_expires_at DATE,
    auto_renew TINYINT(1) DEFAULT 0,
    

    -- Financial
    billing_email VARCHAR(100),
    billing_contact VARCHAR(100),
    billing_address TEXT,
    payment_method VARCHAR(50),
    last_payment_date DATE,
    next_billing_date DATE,
    monthly_fee DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'PGK',
    
    -- Status & Verification
    is_active TINYINT(1) DEFAULT 1,
    is_verified TINYINT(1) DEFAULT 0,
    verified_at DATETIME NULL,
    verified_by INT,
    
    -- Metadata
    industry_sector VARCHAR(100),
    organization_size ENUM('small', 'medium', 'large', 'government') DEFAULT 'medium',
    timezone VARCHAR(50) DEFAULT 'Pacific/Port_Moresby',
    language VARCHAR(5) DEFAULT 'en',
    
    -- SEO
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords VARCHAR(500),
    
    -- Audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_code (code),
    INDEX idx_slug (slug),
    INDEX idx_name (name),
    INDEX idx_active (is_active),
    INDEX idx_subscription_status (subscription_status),
    INDEX idx_subscription_expires (subscription_expires_at),
    FULLTEXT idx_search (name, description),
    FOREIGN KEY (created_by) REFERENCES dakoii_users(id),
    FOREIGN KEY (updated_by) REFERENCES dakoii_users(id),
    FOREIGN KEY (verified_by) REFERENCES dakoii_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Organization Subscription History
CREATE TABLE org_subscription_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    subscription_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    starts_at DATE NOT NULL,
    expires_at DATE NOT NULL,
    amount DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'PGK',
    payment_method VARCHAR(50),
    payment_reference VARCHAR(100),
    notes TEXT,
    changed_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_organization (organization_id),
    INDEX idx_dates (starts_at, expires_at),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES dakoii_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Organization Usage Metrics (Daily snapshots)
CREATE TABLE org_usage_metrics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    metric_date DATE NOT NULL,
    
    -- Usage Counts
    active_users INT DEFAULT 0,
    active_smes INT DEFAULT 0,
    published_products INT DEFAULT 0,
    published_events INT DEFAULT 0,
    
    -- Traffic Metrics
    page_views INT DEFAULT 0,
    unique_visitors INT DEFAULT 0,
    product_views INT DEFAULT 0,
    event_views INT DEFAULT 0,
    
    -- Storage
    storage_used_mb INT DEFAULT 0,
    files_count INT DEFAULT 0,
    images_count INT DEFAULT 0,
    
    -- API Usage (if applicable)
    api_calls INT DEFAULT 0,
    api_bandwidth_mb INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_org_date (organization_id, metric_date),
    INDEX idx_organization (organization_id),
    INDEX idx_date (metric_date),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- ORGANIZATION USERS MANAGEMENT
-- =============================================

-- Organization Users (Managed by Dakoii)
CREATE TABLE org_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    organization_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    profile_photo_path VARCHAR(500),
    
    -- Role & Permissions
    role ENUM('admin', 'manager', 'editor', 'viewer') DEFAULT 'editor',
    permissions JSON, -- Granular permissions
    
    
    -- Status
    is_active TINYINT(1) DEFAULT 1,
    
    -- Session
    last_login DATETIME NULL,
    last_login_ip VARCHAR(45),
    
    -- Audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT, -- Can be dakoii_user_id or org_user_id
    created_by_type ENUM('dakoii', 'organization') DEFAULT 'dakoii',
    updated_by INT,
    updated_by_type ENUM('dakoii', 'organization') DEFAULT 'dakoii',
    
    UNIQUE KEY unique_org_username (organization_id, username),
    UNIQUE KEY unique_org_email (organization_id, email),
    INDEX idx_organization (organization_id),
    INDEX idx_role (role),
    INDEX idx_active (is_active),
    INDEX idx_email (email),
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- PRODUCT TYPES & TAXONOMIES
-- =============================================

-- Product Types (Tourism Categories)
CREATE TABLE product_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    
    -- Hierarchy
    parent_id INT NULL, -- For nested categories
    level INT DEFAULT 1, -- Category depth level
    path VARCHAR(500), -- Full hierarchy path
    
    -- Display
    color_code VARCHAR(7), -- Hex color
    icon_image_path VARCHAR(500),
    display_order INT DEFAULT 0,
    
    
    -- Status
    is_active TINYINT(1) DEFAULT 1,
     
    -- Audit
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT,
    updated_by INT,
    
    INDEX idx_code (code),
    INDEX idx_slug (slug),
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_display_order (display_order),
    FULLTEXT idx_search (name, description),
    FOREIGN KEY (parent_id) REFERENCES product_types(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES dakoii_users(id),
    FOREIGN KEY (updated_by) REFERENCES dakoii_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- SYSTEM CONFIGURATION & SETTINGS
-- =============================================

-- System Settings (Global Configuration)
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category VARCHAR(50) NOT NULL,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    value_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string',
    description TEXT,
    is_public TINYINT(1) DEFAULT 0, -- Public settings visible to organizations
    is_editable TINYINT(1) DEFAULT 1, -- Can be edited through UI
    
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    
    UNIQUE KEY unique_category_key (category, setting_key),
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    FOREIGN KEY (updated_by) REFERENCES dakoii_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- =============================================
-- ACTIVITY LOGS & AUDIT TRAILS
-- =============================================

-- Activity Logs (System-wide Audit Trail)
CREATE TABLE activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- Actor Information
    user_type ENUM('dakoii', 'organization', 'system', 'public') NOT NULL,
    user_id INT,
    username VARCHAR(50),
    
    -- Organization Context
    organization_id INT NULL,
    organization_name VARCHAR(200),
    
    -- Action Details
    action VARCHAR(100) NOT NULL, -- create, update, delete, login, logout, etc.
    action_category VARCHAR(50), -- auth, organization, product, user, system, etc.
    entity_type VARCHAR(50) NOT NULL, -- organization, org_user, product_type, etc.
    entity_id INT,
    entity_name VARCHAR(200),
    
    -- Changes
    old_values JSON,
    new_values JSON,
    changes_summary TEXT,
    
    -- Request Details
    ip_address VARCHAR(45),
    user_agent TEXT,
    request_method VARCHAR(10),
    request_url VARCHAR(500),
    
    -- Result
    status ENUM('success', 'failed', 'error') DEFAULT 'success',
    error_message TEXT,
    
    -- Metadata
    metadata JSON, -- Additional context
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'low',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user (user_type, user_id),
    INDEX idx_organization (organization_id),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_action (action),
    INDEX idx_action_category (action_category),
    INDEX idx_created (created_at),
    INDEX idx_severity (severity),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

'''
-- =============================================
-- ANALYTICS & REPORTING
-- =============================================


---

## Feature 1: Authentication & Access Control

### Feature Description
Secure, multi-layered authentication system for Dakoii super administrators with advanced security features including two-factor authentication, session management, and comprehensive login tracking.

### User Stories

#### US-D1.1: Super Admin Login
**As a** Dakoii super administrator  
**I want to** securely log in to the Dakoii portal with my credentials  
**So that** I can access system-wide administrative functions

**Acceptance Criteria:**
- Login page at `/dakoii/login` with dark themed interface
- Username/email and password authentication
- "Remember me" option for trusted devices
- Password visibility toggle
- Clear error messages for failed attempts
- Account lockout after 5 failed attempts
- Login redirects to dashboard upon success
- Session expires after 2 hours of inactivity

**Technical Notes:**
- Use bcrypt for password hashing
- Implement CSRF protection
- Log all login attempts in `dakoii_login_history`
- Create session record in `dakoii_sessions`

---

#### US-D1.2: Two-Factor Authentication Setup
**As a** Dakoii super administrator  
**I want to** enable two-factor authentication on my account  
**So that** I can add an extra layer of security

**Acceptance Criteria:**
- Option to enable 2FA in profile settings
- QR code generation for authenticator apps
- Backup codes provided (10 codes)
- Ability to test 2FA before activation
- Recovery options if device is lost
- Can disable 2FA with password confirmation

**Technical Notes:**
- Use TOTP (Time-based One-Time Password)
- Store encrypted secret in `two_factor_secret` field
- Generate and store backup codes securely

---

#### US-D1.3: Session Management
**As a** Dakoii super administrator  
**I want to** view and manage my active sessions  
**So that** I can ensure my account security

**Acceptance Criteria:**
- View list of all active sessions
- Display device type, browser, location, and last activity
- Ability to terminate specific sessions
- Ability to terminate all other sessions
- Automatic session cleanup for expired sessions
- Session timeout warning 5 minutes before expiry

**Technical Notes:**
- Query `dakoii_sessions` table
- Update `last_activity` on each request
- Implement session refresh mechanism

---

#### US-D1.4: Role-Based Access Control
**As a** Dakoii super administrator  
**I want to** assign different roles to Dakoii users  
**So that** I can control what each administrator can access

**Acceptance Criteria:**
- Three roles: super_admin, admin, viewer
- Super Admin: Full system access
- Admin: Manage organizations and users, no system settings
- Viewer: Read-only access to reports and data
- Role assignment during user creation
- Role modification by Super Admins only
- Granular permissions stored in JSON

**Technical Notes:**
- Implement middleware for role checking
- Use permissions JSON for fine-grained control
- Log all permission changes

---

#### US-D1.5: Login History & Audit
**As a** Dakoii super administrator  
**I want to** view login history and security events  
**So that** I can monitor account security

**Acceptance Criteria:**
- View chronological list of login attempts
- Filter by success/failed/blocked status
- Display IP address, location, and device
- Flag suspicious login patterns
- Export login history to CSV
- Real-time notifications for failed login attempts

**Technical Notes:**
- Query `dakoii_login_history` table
- Implement GeoIP lookup for locations
- Use chart library for visualization

---

## Feature 2: Organization Management (CRUD)

### Feature Description
Complete lifecycle management of tourism organizations, including creation, configuration, monitoring, and subscription management.

### User Stories

#### US-D2.1: Create New Organization
**As a** Dakoii super administrator  
**I want to** create a new tourism organization  
**So that** I can onboard new regional tourism bodies

**Acceptance Criteria:**
- Form with all organization details
- Auto-generate unique organization code
- Auto-create slug from organization name
- Set subscription type and limits
- Upload logo and banner images (max 5MB each)
- Set billing information
- Enable/disable specific features
- Email notification sent to contact person
- Organization appears in list immediately

**Technical Notes:**
- Validate unique code and slug
- Create organization record in database
- Set default limits based on subscription type
- Initialize usage counters to 0
- Log creation in activity_logs

---

#### US-D2.2: View Organizations List
**As a** Dakoii super administrator  
**I want to** view a paginated list of all organizations  
**So that** I can monitor the system ecosystem

**Acceptance Criteria:**
- Responsive table with key organization info
- Display: Code, Name, Subscription Type, Status, Users, SMEs, Products
- Color-coded subscription status badges
- Search by name, code, or email
- Filter by subscription type, status, country
- Sort by name, created date, expiry date
- 20 items per page with pagination
- Quick actions: View, Edit, Suspend, Delete

**Technical Notes:**
- Query organizations table with joins
- Include usage metrics in display
- Implement server-side pagination
- Add index for common filters

---

#### US-D2.3: View Organization Details
**As a** Dakoii super administrator  
**I want to** view comprehensive details of an organization  
**So that** I can understand their setup and activity

**Acceptance Criteria:**
- Organization profile with all information
- Usage statistics with progress bars
- List of organization admins
- Recent activity timeline
- Content summary (SMEs, Products, Events)
- Subscription history
- Usage metrics chart (last 30 days)
- Quick actions for common tasks

**Technical Notes:**
- Query multiple related tables
- Calculate usage percentages
- Generate activity timeline from logs
- Create usage chart with Chart.js

---

#### US-D2.4: Update Organization
**As a** Dakoii super administrator  
**I want to** modify organization details and settings  
**So that** I can keep information accurate and adjust configurations

**Acceptance Criteria:**
- Edit form pre-filled with current data
- Update all organization fields
- Modify subscription type and limits
- Change subscription dates
- Enable/disable features
- Replace logo and banner
- Update billing information
- See changes history before saving
- Confirmation before major changes

**Technical Notes:**
- Validate all input fields
- Check for unique constraints
- Log old and new values
- Update `updated_by` and `updated_at`
- Create entry in subscription history if subscription changed

---

#### US-D2.5: Suspend/Activate Organization
**As a** Dakoii super administrator  
**I want to** suspend or reactivate an organization  
**So that** I can control access without deleting data

**Acceptance Criteria:**
- Toggle organization status
- Suspended organizations cannot log in
- Public content becomes hidden
- Email notification to organization
- Reason for suspension (required)
- Scheduled reactivation date (optional)
- Log suspension in activity logs
- Can reactivate with single click

**Technical Notes:**
- Update `is_active` and `subscription_status` fields
- Block all org_users from logging in
- Hide organization's public content
- Send automated email notification
- Log suspension reason and date

---

#### US-D2.6: Delete Organization
**As a** Dakoii super administrator  
**I want to** permanently delete an organization  
**So that** I can remove invalid or inactive entities

**Acceptance Criteria:**
- Warning dialog showing data dependencies
- Display count of users, SMEs, products, events
- Require confirmation by typing organization name
- Option to export data before deletion
- Cascading delete of all related records
- Cannot undo deletion (permanent)
- Log deletion with full context
- Success message after completion

**Technical Notes:**
- Check foreign key relationships
- Implement soft delete option (mark as deleted)
- Delete related files from storage
- Use database transactions for safety
- Log complete organization snapshot before deletion

---

#### US-D2.7: Manage Organization Subscription
**As a** Dakoii super administrator  
**I want to** manage organization subscriptions  
**So that** I can handle renewals, upgrades, and billing

**Acceptance Criteria:**
- View current subscription details
- Upgrade/downgrade subscription type
- Extend subscription expiry date
- Set auto-renewal status
- Record payment information
- View subscription history
- Generate invoice/receipt
- Email confirmation of changes
- Update usage limits automatically

**Technical Notes:**
- Update subscription fields in organizations table
- Create record in org_subscription_history
- Adjust max_users, max_smes, max_products limits
- Calculate prorated pricing for upgrades
- Send email with subscription details

---

#### US-D2.8: Monitor Organization Usage
**As a** Dakoii super administrator  
**I want to** monitor organization resource usage  
**So that** I can identify limit issues and usage patterns

**Acceptance Criteria:**
- Dashboard showing current vs. maximum usage
- Visual progress bars for each limit
- Usage trending charts (30, 60, 90 days)
- Alerts when approaching limits (80%, 90%, 100%)
- Export usage reports to CSV
- Compare usage across organizations
- Identify high-usage organizations
- Predict future usage trends

**Technical Notes:**
- Query org_usage_metrics table
- Calculate usage percentages
- Generate charts with Chart.js
- Implement notification triggers
- Create scheduled task for daily usage snapshots

---

## Feature 3: Organization User Management (CRUD)

### Feature Description
Comprehensive management of organization administrator accounts, including creation, role assignment, permissions management, and security controls.

### User Stories

#### US-D3.1: Create Organization Admin
**As a** Dakoii super administrator  
**I want to** create admin accounts for organizations  
**So that** they can manage their tourism content

**Acceptance Criteria:**
- Form to create new org_user
- Select target organization from dropdown
- Enter user details (name, email, username, phone)
- Assign role (admin, manager, editor, viewer)
- Set custom permissions (JSON)
- Generate temporary password
- Send welcome email with login credentials
- Option to require password change on first login
- User appears in organization's user list

**Technical Notes:**
- Validate unique username within organization
- Validate unique email within organization
- Hash password before storage
- Set `created_by_type` to 'dakoii'
- Log creation in activity_logs
- Send automated welcome email

---

#### US-D3.2: View All Organization Users
**As a** Dakoii super administrator  
**I want to** view all organization users across the system  
**So that** I can monitor user access and activity

**Acceptance Criteria:**
- Comprehensive table of all org_users
- Display: Username, Name, Organization, Role, Status, Last Login
- Search by username, name, email, or organization
- Filter by organization, role, active status
- Sort by any column
- Show inactive/suspended users separately
- Quick actions: View, Edit, Reset Password, Delete
- Export user list to CSV

**Technical Notes:**
- Join org_users with organizations table
- Implement efficient pagination
- Add indexes for search performance
- Cache organization names for faster display

---

#### US-D3.3: View Organization Users by Organization
**As a** Dakoii super administrator  
**I want to** view all users belonging to a specific organization  
**So that** I can manage organization team members

**Acceptance Criteria:**
- Filter users by organization
- Display in organization detail page
- Show role distribution (pie chart)
- Display recent activity
- Check against max_users limit
- Warning if approaching user limit
- Quick add user for this organization
- Bulk actions (activate, deactivate, delete)

**Technical Notes:**
- Query org_users WHERE organization_id = X
- Compare current_users to max_users
- Calculate role percentages
- Display activity from login_history

---

#### US-D3.4: Update Organization User
**As a** Dakoii super administrator  
**I want to** modify organization user details and permissions  
**So that** I can maintain accurate access control

**Acceptance Criteria:**
- Edit form pre-filled with current data
- Update name, email, username, phone
- Change role assignment
- Modify custom permissions
- Reassign to different organization (if needed)
- Activate/deactivate account
- Force email verification
- Log all changes
- Email notification to user if major changes

**Technical Notes:**
- Validate unique constraints within new organization
- Log old and new values in activity_logs
- Update `updated_by_type` to 'dakoii'
- Handle organization reassignment carefully
- Send notification email for role changes

---

#### US-D3.5: Reset Organization User Password
**As a** Dakoii super administrator  
**I want to** reset an organization user's password  
**So that** I can help with access issues

**Acceptance Criteria:**
- Generate secure random password
- Option to set custom password
- Force password change on next login
- Send password reset email
- Log password reset action
- Invalidate all existing sessions
- Show success message with masked password
- Option to copy temporary password

**Technical Notes:**
- Generate strong random password (12+ chars)
- Hash password before storage
- Update `last_password_change` timestamp
- Delete existing sessions from org_sessions
- Log in activity_logs
- Send automated email with new credentials

---

#### US-D3.6: Delete Organization User
**As a** Dakoii super administrator  
**I want to** remove organization user accounts  
**So that** I can revoke access when no longer needed

**Acceptance Criteria:**
- Warning dialog before deletion
- Show user's activity summary
- Check for content created by user
- Option to reassign content to another user
- Require confirmation
- Cannot delete last admin of organization
- Log deletion with reason
- Email notification to organization admin

**Technical Notes:**
- Check if user is last admin
- Update created_by/updated_by references
- Delete or anonymize activity logs
- Remove user from database
- Update organization's current_users count
- Log in activity_logs

---

#### US-D3.7: View User Activity History
**As a** Dakoii super administrator  
**I want to** view an organization user's activity history  
**So that** I can monitor usage and troubleshoot issues

**Acceptance Criteria:**
- Timeline of user actions
- Login/logout history
- Content created, updated, deleted
- Failed login attempts
- Permission changes
- Filter by date range
- Filter by action type
- Export activity log to CSV

**Technical Notes:**
- Query activity_logs for specific user
- Format as timeline visualization
- Include IP addresses and devices
- Calculate total actions per day
- Identify unusual patterns

---

## Feature 4: Product Types Management (CRUD)

### Feature Description
System-wide management of tourism product categories and types, including hierarchical organization, custom field configuration, and display settings.

### User Stories

#### US-D4.1: Create Product Type
**As a** Dakoii super administrator  
**I want to** create new tourism product categories  
**So that** organizations can properly classify their offerings

**Acceptance Criteria:**
- Form with all product type fields
- Enter name, code, and description
- Auto-generate slug from name
- Select parent category (for hierarchy)
- Set display order
- Choose icon (icon class or upload SVG)
- Set color code (hex color picker)
- Upload banner image
- Configure required fields (pricing, duration, location, booking)
- Define custom fields (JSON schema)
- Set SEO metadata
- Preview how it will appear on public site

**Technical Notes:**
- Validate unique code and slug
- Calculate level based on parent
- Build path string for hierarchy
- Validate hex color format
- Store custom fields as JSON
- Log creation in activity_logs

---

#### US-D4.2: View Product Types List
**As a** Dakoii super administrator  
**I want to** view all product types in a hierarchical structure  
**So that** I can understand the taxonomy

**Acceptance Criteria:**
- Tree view showing parent-child relationships
- Indentation for nested categories
- Display: Name, Code, Icon, Product Count, Status
- Drag and drop to reorder
- Expand/collapse nested categories
- Color-coded status badges
- Search by name or code
- Filter by active/inactive status
- Quick actions: Edit, Add Child, Delete

**Technical Notes:**
- Query with parent_id relationships
- Build hierarchical tree structure
- Implement drag-drop with JavaScript
- Update display_order on reorder
- Show product_count for each type

---

#### US-D4.3: View Product Type Details
**As a** Dakoii super administrator  
**I want to** view detailed information about a product type  
**So that** I can understand its configuration and usage

**Acceptance Criteria:**
- Complete product type information
- Hierarchy path (breadcrumb)
- Icon and color preview
- Banner image preview
- Custom fields schema
- Configuration settings display
- Usage statistics (total products, by organization)
- List of products using this type
- SEO information
- Audit trail (created by, updated by, dates)

**Technical Notes:**
- Build full hierarchy path
- Query products table for count
- Group products by organization
- Display custom fields in readable format
- Show activity history from logs

---

#### US-D4.4: Update Product Type
**As a** Dakoii super administrator  
**I want to** modify product type details and configuration  
**So that** I can improve categorization over time

**Acceptance Criteria:**
- Edit form pre-filled with current data
- Update all fields except code (immutable)
- Change parent category
- Modify display order
- Update icon and banner
- Adjust custom fields schema
- Change configuration settings
- Update SEO metadata
- Warning if changing parent affects hierarchy
- Preview changes before saving

**Technical Notes:**
- Recalculate level and path if parent changed
- Update all child categories if hierarchy changed
- Validate custom fields schema (JSON)
- Log old and new values
- Update products if custom fields changed

---

#### US-D4.5: Reorder Product Types
**As a** Dakoii super administrator  
**I want to** change the display order of product types  
**So that** I can control how they appear to users

**Acceptance Criteria:**
- Drag and drop interface
- Visual feedback during drag
- Reorder within same level only
- Save order automatically
- Undo last reorder action
- Bulk reorder with manual sort values
- Apply alphabetical sorting option
- Preview order on public site

**Technical Notes:**
- Update display_order field
- Use AJAX for smooth updates
- Implement optimistic UI updates
- Log order changes
- Use transactions for bulk updates

---

#### US-D4.6: Delete Product Type
**As a** Dakoii super administrator  
**I want to** remove unused product types  
**So that** I can keep the taxonomy clean

**Acceptance Criteria:**
- Warning if products exist with this type
- Display count of affected products
- Option to reassign products to different type
- Cannot delete if has child categories
- Require confirmation
- Log deletion with reason
- Success message after completion

**Technical Notes:**
- Check for existing products
- Check for child categories
- Update products.product_type_id if reassigning
- Delete from database
- Log in activity_logs with full snapshot

---

#### US-D4.7: Configure Product Type Fields
**As a** Dakoii super administrator  
**I want to** define custom fields for product types  
**So that** different types can collect relevant information

**Acceptance Criteria:**
- Visual form builder interface
- Add/remove custom fields
- Field types: text, number, date, dropdown, checkbox, textarea
- Set field properties: label, required, default value, validation
- Reorder fields
- Preview form as organizations will see it
- Save field schema as JSON
- Validate against existing products
- Migration tool if schema changes

**Technical Notes:**
- Store schema in custom_fields JSON column
- Implement JSON schema validation
- Create UI with drag-drop form builder
- Version control for schema changes
- Provide migration scripts for schema updates

---

## Feature 5: System Dashboard & Analytics

### Feature Description
Comprehensive overview dashboard providing system-wide metrics, insights, and quick access to critical functions.

### User Stories

#### US-D5.1: View System Dashboard
**As a** Dakoii super administrator  
**I want to** see a comprehensive system overview  
**So that** I can monitor overall system health

**Acceptance Criteria:**
- Dark-themed dashboard with stat cards
- Key metrics: Total Organizations, Active Organizations, Total Users, Active Users
- Content metrics: Total Products, Total Events, Total SMEs
- Financial metrics: Monthly Revenue, Subscriptions Due
- Usage trends chart (last 30 days)
- Recent activity feed
- System health indicators
- Quick action buttons
- Alerts and notifications panel
- Refreshes automatically every 5 minutes

**Technical Notes:**
- Query multiple tables for metrics
- Calculate trends with date comparisons
- Use Chart.js for visualizations
- Implement real-time updates with AJAX
- Cache dashboard data for performance

---

#### US-D5.2: View Organizations Analytics
**As a** Dakoii super administrator  
**I want to** analyze organization distribution and growth  
**So that** I can understand system adoption

**Acceptance Criteria:**
- Organizations by subscription type (pie chart)
- Organizations by status (active, trial, expired)
- Organizations by country/region (map)
- Growth trend chart (monthly new organizations)
- Churn rate analysis
- Average subscription duration
- Top organizations by usage
- Organizations nearing limits

**Technical Notes:**
- Query organizations table with aggregations
- Calculate growth rates
- Use geographic data for map visualization
- Implement date range filters
- Export charts to PDF

---

#### US-D5.3: View User Analytics
**As a** Dakoii super administrator  
**I want to** analyze user activity and engagement  
**So that** I can measure system adoption

**Acceptance Criteria:**
- Total users breakdown (Dakoii vs Organization)
- Active users (logged in last 30 days)
- User growth trend
- Most active users
- Login frequency distribution
- Failed login attempts summary
- User roles distribution
- Geographic distribution of users

**Technical Notes:**
- Query dakoii_users and org_users tables
- Join with login_history for activity
- Calculate engagement metrics
- Create heatmap for login times
- Use GeoIP for location data

---

#### US-D5.4: View Content Analytics
**As a** Dakoii super administrator  
**I want to** analyze content creation and distribution  
**So that** I can understand system usage patterns

**Acceptance Criteria:**
- Total content count (SMEs, Products, Events)
- Content by organization
- Content by product type
- Publishing trends (daily, weekly, monthly)
- Most popular product types
- Content quality metrics
- Draft vs Published ratio
- Content growth trends

**Technical Notes:**
- Query smes, products, events tables
- Aggregate by organization and type
- Calculate trends over time
- Identify content gaps
- Create content health score

---

#### US-D5.5: View Financial Analytics
**As a** Dakoii super administrator  
**I want to** analyze revenue and subscription metrics  
**So that** I can understand financial performance

**Acceptance Criteria:**
- Monthly recurring revenue (MRR)
- Revenue by subscription type
- Revenue trend chart
- Upcoming renewals
- Payment success rate
- Overdue payments
- Average revenue per organization
- Subscription conversion rates (trial to paid)

**Technical Notes:**
- Query organizations for subscription data
- Join with subscription_history
- Calculate MRR and ARR
- Identify at-risk subscriptions
- Generate financial forecasts

---

## Feature 6: System Configuration & Settings

### Feature Description
Global system configuration management including settings, notifications, email templates, and system maintenance tools.

### User Stories

#### US-D6.1: Manage System Settings
**As a** Dakoii super administrator  
**I want to** configure global system settings  
**So that** I can control system behavior

**Acceptance Criteria:**
- Organized settings by category
- Categories: General, Security, Email, Storage, API, Billing
- Text, number, boolean, and JSON value types
- Setting descriptions and default values
- Validation for each setting
- Save individual or bulk settings
- Reset to default option
- Changes logged in activity_logs
- Some settings require restart notification

**Technical Notes:**
- Query system_settings table
- Group by category
- Validate based on value_type
- Cache settings for performance
- Log all setting changes

---

#### US-D6.2: Configure Email Templates
**As a** Dakoii super administrator  
**I want to** customize email templates  
**So that** communications are professional and branded

**Acceptance Criteria:**
- List of all email templates
- Template editor with variable placeholders
- Preview with sample data
- Support for HTML and plain text
- Test email sending
- Template versioning
- Default templates available
- Variables documented ({{username}}, {{organization}}, etc.)
- Save and revert changes

**Technical Notes:**
- Store templates in database or files
- Use template engine (e.g., Twig, Blade)
- Validate variable usage
- Implement preview rendering
- Queue test emails

---

#### US-D6.3: Manage Notifications
**As a** Dakoii super administrator  
**I want to** configure system notifications  
**So that** I can control alerts and communications

**Acceptance Criteria:**
- Enable/disable notification types
- Set notification triggers and conditions
- Configure notification channels (email, in-app, SMS)
- Set notification frequency limits
- Create custom notification templates
- Test notifications
- View notification history
- Bulk notification management

**Technical Notes:**
- Query system_notifications table
- Implement notification queue
- Support multiple channels
- Rate limiting for notifications
- Log all notifications sent

---

#### US-D6.4: View System Logs
**As a** Dakoii super administrator  
**I want to** access comprehensive system logs  
**So that** I can troubleshoot issues and audit activity

**Acceptance Criteria:**
- Filterable log viewer
- Filter by: date range, user type, action, entity, severity
- Search by keyword
- Export logs to CSV
- Real-time log streaming
- Log rotation settings
- Archive old logs
- Error log highlighting
- Performance logs (slow queries)

**Technical Notes:**
- Query activity_logs table
- Implement efficient pagination
- Use indexed searches
- Stream logs with WebSockets
- Implement log retention policy

---

#### US-D6.5: System Maintenance Tools
**As a** Dakoii super administrator  
**I want to** perform system maintenance tasks  
**So that** I can keep the system healthy

**Acceptance Criteria:**
- Clear cache (all or selective)
- Rebuild indexes
- Optimize database tables
- Clean up expired sessions
- Remove old activity logs
- Purge deleted files
- Run database migrations
- System health check
- Backup database
- View system information (PHP, MySQL versions, disk space)

**Technical Notes:**
- Implement CLI commands for tasks
- Provide web interface to trigger
- Show progress indicators
- Log all maintenance actions
- Schedule automated maintenance

---

## Feature 7: Reports & Exports

### Feature Description
Comprehensive reporting system with customizable reports, scheduled generation, and multiple export formats.

### User Stories

#### US-D7.1: Generate Organization Report
**As a** Dakoii super administrator  
**I want to** generate detailed organization reports  
**So that** I can analyze organizational performance

**Acceptance Criteria:**
- Select date range
- Choose organizations (all or specific)
- Include/exclude metrics: users, content, usage, financial
- Choose format: PDF, Excel, CSV
- Schedule recurring reports
- Email report when ready
- Save report templates
- Preview before generating

**Technical Notes:**
- Use reporting library (e.g., Laravel Excel, TCPDF)
- Generate reports asynchronously
- Store generated reports temporarily
- Implement report templates
- Queue report generation

---

#### US-D7.2: Generate User Activity Report
**As a** Dakoii super administrator  
**I want to** generate user activity reports  
**So that** I can monitor system usage

**Acceptance Criteria:**
- Select user type (Dakoii or Organization)
- Date range selection
- Filter by organization
- Include: logins, actions, content created
- Activity heatmap
- Export to PDF, Excel, CSV
- Scheduled reports
- Comparative analysis (period over period)

**Technical Notes:**
- Query activity_logs and login_history
- Aggregate by user and date
- Generate visualizations
- Use background job for large reports
- Cache report data

---

#### US-D7.3: Generate Financial Report
**As a** Dakoii super administrator  
**I want to** generate financial and subscription reports  
**So that** I can track revenue and billing

**Acceptance Criteria:**
- Revenue summary by period
- Subscription breakdown
- Payment success/failure rates
- Upcoming renewals
- Overdue accounts
- Subscription changes (upgrades/downgrades)
- Export to PDF, Excel
- Scheduled monthly reports
- Send to finance team

**Technical Notes:**
- Query organizations and subscription_history
- Calculate financial metrics
- Generate invoice-style reports
- Implement PDF generation
- Schedule automated reports

---

#### US-D7.4: Generate Content Report
**As a** Dakoii super administrator  
**I want to** generate content analytics reports  
**So that** I can understand content trends

**Acceptance Criteria:**
- Content overview (SMEs, Products, Events)
- Content by organization
- Content by product type
- Publishing trends
- Content quality scores
- Most viewed content
- Export to PDF, Excel, CSV
- Visual charts included

**Technical Notes:**
- Query content tables
- Calculate quality metrics
- Generate charts with Chart.js
- Export charts in report
- Implement pagination for large datasets

---

#### US-D7.5: Export System Data
**As a** Dakoii super administrator  
**I want to** export system data in various formats  
**So that** I can perform external analysis

**Acceptance Criteria:**
- Choose data tables to export
- Select fields to include
- Filter and sort options
- Export formats: CSV, Excel, JSON, XML
- Large exports handled with queue
- Download link sent via email
- Scheduled exports
- Export history tracking

**Technical Notes:**
- Implement chunked exports for large data
- Use queue for async processing
- Generate secure download links
- Set expiration on exports
- Log all data exports

---

## Feature 8: Notifications & Alerts

### Feature Description
Comprehensive notification system for system events, alerts, and communications with intelligent prioritization and delivery.

### User Stories

#### US-D8.1: View Notifications Dashboard
**As a** Dakoii super administrator  
**I want to** see all system notifications in one place  
**So that** I can stay informed of important events

**Acceptance Criteria:**
- Notifications bell icon in navbar
- Badge showing unread count
- Dropdown showing recent notifications
- Color-coded by severity (info, warning, error, critical)
- Click to view full details
- Mark as read/unread
- Dismiss notifications
- Filter by severity and type
- Link to relevant pages
- Real-time updates

**Technical Notes:**
- Query system_notifications table
- Filter by target (dakoii_user or all_dakoii)
- Update read status on click
- Use WebSockets for real-time
- Implement notification sound (optional)

---

#### US-D8.2: Configure Alert Rules
**As a** Dakoii super administrator  
**I want to** set up automated alerts  
**So that** I'm notified of critical events

**Acceptance Criteria:**
- Create custom alert rules
- Conditions: subscription expiring, limit reached, failed payments, etc.
- Set thresholds (e.g., alert when 80% of limit reached)
- Choose notification channels
- Set alert frequency (immediate, daily digest, weekly)
- Enable/disable rules
- Test alert rules
- View alert history

**Technical Notes:**
- Store rules in database
- Implement rule evaluation engine
- Schedule periodic checks
- Create notifications based on rules
- Log all triggered alerts

---

#### US-D8.3: Send Broadcast Notifications
**As a** Dakoii super administrator  
**I want to** send notifications to all or specific users  
**So that** I can communicate important information

**Acceptance Criteria:**
- Choose target audience (all dakoii, all orgs, specific orgs)
- Set notification severity
- Write title and message
- Add action button with URL
- Preview notification
- Schedule send time
- Track delivery and read rates
- Include attachments (optional)

**Technical Notes:**
- Create notification records for each target
- Queue for bulk sending
- Track read status
- Support rich text formatting
- Implement scheduling system

---

## Feature 9: Security & Compliance

### Feature Description
Advanced security features including audit trails, compliance reporting, data privacy controls, and security monitoring.

### User Stories

#### US-D9.1: View Security Dashboard
**As a** Dakoii super administrator  
**I want to** monitor system security status  
**So that** I can identify and respond to threats

**Acceptance Criteria:**
- Security health score
- Recent failed login attempts
- Suspicious activity alerts
- Active sessions count
- Password strength analysis
- Two-factor adoption rate
- Recent security events timeline
- Vulnerabilities detected
- Security recommendations

**Technical Notes:**
- Analyze login_history for patterns
- Calculate security metrics
- Implement threat detection algorithms
- Generate security score
- Display in visual dashboard

---

#### US-D9.2: Audit Trail Search
**As a** Dakoii super administrator  
**I want to** search and analyze audit trails  
**So that** I can investigate specific events

**Acceptance Criteria:**
- Advanced search filters
- Search by user, action, entity, date range
- Full-text search in changes
- Export audit records
- Generate audit reports
- Compare before/after values
- Flag suspicious activities
- Regulatory compliance filters

**Technical Notes:**
- Query activity_logs with complex filters
- Implement full-text search
- Display changes in diff format
- Create comprehensive audit reports
- Support compliance standards (SOC 2, GDPR)

---

#### US-D9.3: Data Privacy Controls
**As a** Dakoii super administrator  
**I want to** manage data privacy settings  
**So that** I can ensure compliance with regulations

**Acceptance Criteria:**
- Configure data retention policies
- Set up automated data purging
- Manage user data requests (GDPR)
- Export user data on request
- Delete user data on request
- Anonymize historical data
- Cookie consent management
- Privacy policy versioning

**Technical Notes:**
- Implement data retention scheduler
- Create data export tools
- Implement right-to-be-forgotten
- Anonymize identifying information
- Track consent management

---

## Feature 10: API Management (Future Enhancement)

### Feature Description
API access management for organizations and third-party integrations.

### User Stories

#### US-D10.1: Manage API Access
**As a** Dakoii super administrator  
**I want to** control API access for organizations  
**So that** I can enable secure integrations

**Acceptance Criteria:**
- Enable/disable API access per organization
- Generate API keys
- Set rate limits
- Monitor API usage
- View API logs
- Revoke API keys
- API documentation links
- Usage alerts

**Technical Notes:**
- Generate secure API keys
- Implement rate limiting
- Log all API calls
- Monitor bandwidth usage
- Create API dashboard

---

## Non-Functional Requirements

### Performance
- Page load time < 2 seconds
- Dashboard refresh < 1 second
- Search results < 500ms
- Report generation queued for > 10 seconds
- Support 100+ concurrent admin users

### Security
- AES-256 encryption for sensitive data
- SSL/TLS for all connections
- Regular security audits
- Penetration testing quarterly
- OWASP Top 10 compliance

### Scalability
- Support 1000+ organizations
- Handle 100,000+ products
- Process 1M+ page views/month
- Database optimization for large datasets
- CDN for static assets

### Availability
- 99.9% uptime SLA
- Automated backups every 6 hours
- Disaster recovery plan
- Failover systems
- Load balancing

### Usability
- Intuitive dark-themed interface
- Mobile-responsive admin panel
- Contextual help system
- Keyboard shortcuts
- Accessibility (WCAG 2.1 Level AA)

### Compliance
- GDPR compliance
- Data privacy regulations
- Industry security standards
- Audit trail for all actions
- Regular compliance reporting

---

## Success Metrics

### Portal Adoption
- 90% of created organizations active within 30 days
- Average time to onboard organization < 30 minutes
- Super admin satisfaction score > 4.5/5

### System Performance
- Dashboard load time average < 1.5 seconds
- Report generation success rate > 99%
- Zero critical security incidents

### User Efficiency
- Average time to create organization < 10 minutes
- Average time to create org user < 5 minutes
- Average time to create product type < 8 minutes
- Task completion rate > 95%

### System Health
- Database query performance index > 90
- Storage optimization ratio > 80%
- API response time average < 200ms
- System uptime > 99.9%

---

This comprehensive feature design and user story document for the Dakoii Portal provides a complete blueprint for developing a powerful, secure, and efficient super administrative interface for the Wanspeen Tourism Product Information System. The dark color scheme creates a professional, authoritative environment that clearly distinguishes the super admin portal from the organization portal while maintaining excellent usability and accessibility.